#!/usr/bin/env python
#
# -*- coding: UTF-8 -*-
#

import importlib
import logging
import os
import shutil
import sys
import zipfile
import torch
from converter.torch import dump_meta_pb2
from converter.converter import Converter, ConverterConfig
from utils.package_checker import is_package_installed


if not is_package_installed('flash_attn'):
    # 会使用到flash_attn里面的so
    import flash_attn
    logging.info("flash_attn imported")
else:
    logging.info("flash_attn not imported")


class TorchAotiConverter(Converter):
    def __init__(self, config: ConverterConfig):
        super().__init__(config)

    def convert(self):
        model_path = self.config.model_path
        origin_model = self.config.origin_model_name
        origin_version = self.config.origin_model_version

        export_dir = os.path.join(model_path, "dense", "export")
        aot_dir = os.path.join(model_path, "dense", "aot")
        triton_ops_dir = os.path.join(model_path, "dense", "triton_ops")
        pb_file = os.path.join(export_dir, "aot_graph.pb")

        graph = dump_meta_pb2.Graph()
        with open(pb_file, "rb") as f:
            graph.ParseFromString(f.read())

        # 加载triton算子
        if graph.triton_ops:
            if triton_ops_dir not in sys.path:
                sys.path.insert(0, triton_ops_dir)
            for op in graph.triton_ops:
                with zipfile.ZipFile(os.path.join(model_path, op), "r") as zipf:
                    zipf.extractall(triton_ops_dir)
                op_name = os.path.basename(op).split(".zip")[0]
                importlib.import_module(op_name)

        for sub_graphs in graph.sub_graphs:
            file_name = os.path.join(model_path, sub_graphs.file_name)
            target_file = os.path.join(aot_dir, os.path.basename(sub_graphs.file_name))

            logging.info(f"try aoti compile {file_name}")

            exported_program = torch.export.load(file_name)
            if self.config.target_device == "cpu":
                exported_program = torch.export.passes.move_to_device_pass(exported_program, torch.device("cpu"))
                logging.info(f"exported_program.example_inputs: {exported_program.example_inputs}")
                exported_program.example_inputs = (  # tuple[tuple[dict], dict]
                    ({k: v.to(torch.device("cpu")) for k, v in exported_program.example_inputs[0][0].items()},),
                    exported_program.example_inputs[1],
                )
                logging.info(f"changed exported_program.example_inputs: {exported_program.example_inputs}")

            torch._inductor.aoti_compile_and_package(exported_program, package_path=target_file)
            logging.info(f"aoti compile {sub_graphs.name} success")

            if self.config.run_test_bs > 0:
                logging.info(f"run aot model {sub_graphs.name}")

                def clip_batch_size(tensor_data, target_batch_size):
                    """裁剪tensor的batch size到目标值，如果小于目标值则不裁剪"""
                    if isinstance(tensor_data, torch.Tensor):
                        if tensor_data.shape[0] > target_batch_size:
                            return tensor_data[:target_batch_size]
                        return tensor_data
                    elif isinstance(tensor_data, dict):
                        clipped_dict = {}
                        for key, value in tensor_data.items():
                            clipped_dict[key] = clip_batch_size(value, target_batch_size)
                        return clipped_dict
                    else:
                        # 对于其他类型，直接返回原数据
                        return tensor_data

                inputs = []
                for input in sub_graphs.inputs:
                    loaded_tensor = torch.load(
                        os.path.join(model_path, input.example_file),
                        map_location=(torch.device("cpu") if self.config.target_device == "cpu" else None),
                    )
                    clipped_tensor = clip_batch_size(loaded_tensor, self.config.run_test_bs)
                    inputs.append(clipped_tensor)

                logging.info(f"inputs: {inputs}")
                aot_model = torch._inductor.aoti_load_package(target_file)
                if sub_graphs.kwargs_file_name != "":
                    kwargs = torch.load(os.path.join(model_path, sub_graphs.kwargs_file_name))
                    aot_output = aot_model(*inputs, **kwargs)
                else:
                    aot_output = aot_model(*inputs)

                logging.warning(f"aot output: {aot_output}")

                outputs = []
                for output in sub_graphs.outputs:
                    loaded_output = torch.load(os.path.join(model_path, output.example_file))
                    clipped_output = clip_batch_size(loaded_output, self.config.run_test_bs)
                    outputs.append(clipped_output)
                logging.warning(f"origin outputs: {outputs}")

        logging.info(f"convert {origin_model}/{origin_version} success")
        logging.info(f"delete triton_ops dir: {triton_ops_dir}")
        shutil.rmtree(triton_ops_dir)
