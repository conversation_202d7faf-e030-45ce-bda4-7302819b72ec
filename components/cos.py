import json
import logging
import os
import random
import requests
from typing import Optional

from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client

from components import const
from utils.with_retry_wrapper import with_retry


class COSConfig(object):
    def __init__(self, bucket: str, secret_id: str, secret_key: str, region: str, sid: str, endpoint: str, token: str=None):
        self.bucket = bucket
        self.secret_id = secret_id
        self.secret_key = secret_key
        self.region = region
        self.sid = sid
        self.endpoint = endpoint
        self.token = token


class COSManager(object):
    def __init__(self, cos_config: COSConfig):
        self.bucket = cos_config.bucket
        self.secret_id = cos_config.secret_id
        self.secret_key = cos_config.secret_key
        self.region = cos_config.region
        self.endpoint = 'cos-internal.%s.tencentcos.cn' % cos_config.region
        self.sid = cos_config.sid
        self.token = cos_config.token
        self.scheme = 'http'

        # 配置和客户端对象
        self.config: Optional[COSConfig] = None
        self.client: Optional[CosS3Client] = None

    @staticmethod
    def get_ip_port_from_sid(sid: str):
        data = {'type': 1, 'service': {'namespace': 'Production', 'name': sid}}
        ret = requests.post('http://polaris-discover.oa.com:8080/v1/Discover',
                            headers={'Content-Type': 'application/json'},
                            data=json.dumps(data))

        ret_body = ret.json()
        instances = ret_body['instances']
        if len(instances) == 0:
            raise Exception('Unable to get instance from L5 SID: %s' % sid)

        selected_instance = random.choice(instances)
        ip = selected_instance['host']
        port = str(selected_instance['port'])
        return ip, port

    def init_client(self):
        self.config = CosConfig(Region=self.region, SecretId=self.secret_id, SecretKey=self.secret_key,
                                Endpoint=self.endpoint, Token=self.token, Scheme=self.scheme)

        self.client = CosS3Client(self.config, retry=const.RETRY_COUNT)

    @with_retry(retry_count=const.RETRY_COUNT, interval=3, backoff=2)
    def download_single_file(self, local_filename: str, cos_filename: str):
        logging.info('Downloading COS file from %s to %s...' % (cos_filename, local_filename))
        if os.path.exists(local_filename):
            logging.info('Local file %s already exists, skip...' % local_filename)
            return

        dir_name = os.path.dirname(local_filename)
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)

        self.client.download_file(
            Bucket=self.bucket,
            Key=cos_filename,
            DestFilePath=local_filename,
            MAXThread=5,
            TrafficLimit='838860800',
        )

    @with_retry(retry_count=const.RETRY_COUNT, interval=3, backoff=2)
    def upload_single_file(self, local_filename: str, cos_filename: str):
        logging.info('Uploading file from %s to COS %s...' % (local_filename, cos_filename))
        response = self.client.upload_file(
            Bucket=self.bucket, Key=cos_filename, LocalFilePath=local_filename, PartSize=4, MAXThread=5)
        return response

    @with_retry(retry_count=const.RETRY_COUNT, interval=3, backoff=2)
    def copy_file(self, src_filename: str, dest_filename: str):
        logging.info('Copying COS file from %s to %s...' % (src_filename, dest_filename))
        response = self.client.copy(
            Bucket=self.bucket,
            Key=dest_filename,
            CopySource={
                'Bucket': self.bucket,
                'Key': src_filename,
                'Region': self.region,
                'Endpoint': self.endpoint
            }
        )
        
        return response
