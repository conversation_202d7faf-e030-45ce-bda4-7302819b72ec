import logging
import subprocess
import os
from components import const
from utils.with_retry_wrapper import with_retry


def _download_file(download_command, file_path, expected_sha1=None, sha1_calculator=None):
    """
    执行下载命令并验证结果

    参数:
        download_command: 下载命令字符串
        file_path: 下载文件的保存路径
        expected_sha1: 期望的SHA1值，如果为None则不验证SHA1
        sha1_calculator: 计算SHA1的函数

    返回:
        bool: 下载是否成功

    抛出:
        Exception: 如果下载失败或验证失败
    """
    # 执行下载命令
    logging.info(f'Start to downloaded file: {file_path}')
    result = subprocess.run(download_command, shell=True, check=True, capture_output=True)

    # 记录命令输出，可能对调试有帮助
    stdout = result.stdout.decode("utf-8", errors="ignore")
    stderr = result.stderr.decode("utf-8", errors="ignore")
    if stderr and not stdout:
        logging.warning(f'Download command produced stderr without stdout: {stderr}')

    # 如果需要验证SHA1
    if expected_sha1 and sha1_calculator:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Downloaded file not found at {file_path}")

        # 检查下载后的文件SHA1是否正确
        actual_sha1 = sha1_calculator(file_path)
        if actual_sha1 != expected_sha1:
            raise ValueError(f'Downloaded file has incorrect SHA1 checksum. Expected: {expected_sha1}, Got: {actual_sha1}')

    logging.info(f'File downloaded successfully: {file_path}')
    return True


@with_retry(retry_count=const.RETRY_COUNT, interval=10.0, backoff=2.0)
def download_with_retry(download_command, file_path, expected_sha1=None, sha1_calculator=None):
    """
    执行下载命令并进行重试，可选择验证下载文件的SHA1值

    参数:
        download_command: 下载命令字符串
        file_path: 下载文件的保存路径
        expected_sha1: 期望的SHA1值，如果为None则不验证SHA1
        sha1_calculator: 计算SHA1的函数，接收文件路径参数，返回SHA1字符串
        max_retries: 最大重试次数 (注意: 实际使用装饰器中的值)
        retry_interval: 重试间隔时间(秒) (注意: 实际使用装饰器中的值)

    返回:
        bool: 下载是否成功
    """
    # 注意: max_retries 和 retry_interval 参数保留是为了保持兼容性，实际上使用的是装饰器中的值
    return _download_file(download_command, file_path, expected_sha1, sha1_calculator)
